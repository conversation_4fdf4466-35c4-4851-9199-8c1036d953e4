version: '3.9'

services:

  minio:
    image: minio/minio:RELEASE.2023-05-04T21-44-30Z
    restart: always
    ports:
      - 9000:9000
      - 9001:9001
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: admin123
      MINIO_ROOT_PASSWORD: admin123
    healthcheck:
      test: ["CMD", "curl", "-f", http://localhost:9000/minio/health/live]
      interval: 30s
      timeout: 20s
      retries: 3