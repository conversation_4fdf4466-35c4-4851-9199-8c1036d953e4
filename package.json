{"name": "upload-service", "version": "1.0.0", "description": "", "main": "build/index.js", "scripts": {"start": "nodemon --watch \"./**\"  --ext \"ts,json\" --exec \"ts-node index.ts\"", "setup:prod": "npm install && npm run build", "setup:dev": "npm install", "build": "tsc"}, "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/multipart": "^9.2.1", "@fastify/rate-limit": "^10.3.0", "aws-sdk": "^2.1645.0", "axios": "^1.10.6", "dotenv": "^12.0.4", "fastify": "^5.4.0", "helocore": "^1.1.8", "minio": "^7.1.0", "nanoid": "^3.1.32", "pino": "^8.11.0", "reflect-metadata": "^0.1.13", "zod": "^3.21.4"}, "devDependencies": {"@prisma/client": "^4.2.1", "@types/dotenv": "^8.2.0", "@types/minio": "^7.0.18", "@types/node": "^24.3.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}