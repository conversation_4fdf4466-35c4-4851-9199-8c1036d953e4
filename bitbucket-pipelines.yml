# This is an example Starter pipeline configuration
# Use a skeleton to build, test and deploy using manual and parallel steps
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
# v3
image: node:22

pipelines:
  branches:
    master:
      - step:
          name: 'Upload Service Updated!'
          script:
            - pipe: atlassian/ssh-run:0.4.1
              variables:
                SSH_USER: $THINKERUSER
                SERVER: $THINKERHOST
                PORT: $THINKERPORT
                COMMAND: >
                  cd /data/web/node/other/upload-service &&
                  bash ./refresh.sh
                MODE: command