import { injectable } from 'helocore'
import { Client } from 'minio'

import config from '../config'
import { TFile } from '../types/upload'

@injectable()
export default class Minio {
  constructor() {
    this.createMinio()
  }

  public minioClient: Client

  public getMinioClient(): Client {
    return this.minioClient
  }

  public createMinio() {
    this.minioClient = new Client({
      endPoint: config.MINIO_URL,
      port: config.MINIO_PORT,
      useSSL: true,
      accessKey: config.MINIO_ACCES_KEY,
      secretKey: config.MINIO_SECRET_KEY
    })
  }

  public async putFile(fileName: string, file: TFile, bucketName: string) {
    await this.minioClient.putObject(bucketName, fileName, file.data, 1, { 'Content-Type': file.mimetype })
  }

  public async getFileUrl(fileName: string, bucketName: string): Promise<string> {
    const url = await this.minioClient.presignedUrl('GET', bucketName, fileName)
  
    const originalUrl = new URL(url)
    const modifiedUrl = originalUrl.origin + originalUrl.pathname
    return modifiedUrl
  }

  public async removeFile(fileName: string, bucketName: string): Promise<void> {
    await this.minioClient.removeObject(bucketName, fileName)
  }

}