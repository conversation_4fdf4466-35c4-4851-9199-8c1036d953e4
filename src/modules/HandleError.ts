import { Zod<PERSON>rror } from "zod"
import pino from "pino"
import enums from "../libs/enums"
import ServiceResponse from "../models/ServiceResponse"

export async function handleError(error, traceId: string, step: string, lang: string): Promise<ServiceResponse<any>> {
  const serviceResponse = new ServiceResponse()
  serviceResponse.status_code = 400
  serviceResponse.success = false
  serviceResponse.trace_id = traceId

  if (error.code === 'FST_REQ_FILE_TOO_LARGE') {
    error = {
      message: error.code
    }
    serviceResponse.message = error.message
    serviceResponse.error_code = enums.error_codes.type
  } else if (error instanceof TypeError) {
    serviceResponse.message = 'global.type_error'
    serviceResponse.error_code = enums.error_codes.type
  } else if (error instanceof ZodError) {
    serviceResponse.status_code = 422
    serviceResponse.message = 'global.validation_zod_error'
    serviceResponse.errors = error.issues.map(a => {
      return {
        message: a.message,
        fields: a.path
      }
    })
    serviceResponse.error_code = enums.error_codes.validation
  } else if (error instanceof ServiceResponse) {
    serviceResponse.message = error.message
  } else if (typeof error === 'string') {
    serviceResponse.message = error
    serviceResponse.error_code = enums.error_codes.bad_request
  } if (error instanceof Error) {
    serviceResponse.message = error.message
    serviceResponse.error_code = enums.error_codes.bad_request
  } else {
    serviceResponse.message = 'global.bad_request'
    serviceResponse.error_code = enums.error_codes.bad_request
  }

  pino().error({
    trace_id: traceId,
    step: step,
    error: serviceResponse.message,
    data: JSON.stringify(error),
    timestamp: new Date()
  })

  return serviceResponse
}