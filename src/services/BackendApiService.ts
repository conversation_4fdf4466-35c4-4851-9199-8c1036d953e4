import { injectable } from 'helocore'

import config from '../config'
import axios from 'axios';

@injectable()
export default class BackendApiService {
  constructor(
  ) { }
  async GetChildBMInfo(token:string,childBmId:string): Promise<{ad_account_id:string,access_token:string}> {
    return axios.get(config.BACKEND_URL + `/agent-app/meta-ads/child-bms/${childBmId}/get-child-bm-info`,{
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(response => response.data.data)
  }
}
