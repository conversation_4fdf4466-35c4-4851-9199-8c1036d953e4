import aws from 'aws-sdk'
import { injectable } from 'helocore'
import config from '../config'
import LoggerService from './LoggerService'

process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = '1'

@injectable()
export default class CloudflareR2Service {
  s3: aws.S3 = new aws.S3({
    endpoint: `https://${config.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: config.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: config.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    signatureVersion: 'v4'
  })

  constructor(
    private readonly loggerService: LoggerService
  ) { }

  async getPresignedUrl(mediaName: string, traceId: string, bucketName = 'helorobo') {
    const url = await this.s3.getSignedUrlPromise('putObject', {
      Bucket: bucketName,
      Key: mediaName,
    }) // string olarak tek kullanımlık url döner

    const objectUrl = await this.s3.getSignedUrlPromise('getObject', {
      Bucket: bucketName,
      Key: mediaName,
    })

    this.loggerService.info({
      trace_id: traceId,
      message: 'url oluşturuldu',
      data: JSON.stringify({
        media_name: mediaName,
        bucket_name: bucketName,
        url: url,
        object_url: objectUrl
      })
    })

    const urlObject = new URL(objectUrl)

    return {
      url: url,
      object_url: config.CLOUDFLARE_R2_BASE_URL + urlObject.pathname,
      public_url: objectUrl
    }
  }
}
