import { injectable } from 'helocore'
import pino, { Logger } from 'pino'

type TWarn = {
  trace_id?: string,
  message: string,
  error?: string,
  data?: string
}

type TInfo = {
  trace_id?: string,
  message: string,
  data?: string
}

type TError = {
  trace_id?: string,
  message: string,
  error: string,
  data: string
}

@injectable()
export default class LoggerService {
  logger: Logger

  constructor() {
    this.logger = pino()
  }

  info(data: TInfo) {
    this.logger.info({
      ...data,
      timestamp: new Date()
    })
  }

  error(data: TError) {
    this.logger.error({
      ...data,
      timestamp: new Date()
    })
  }

  warn(data: TWarn) {
    this.logger.warn({
      ...data,
      timestamp: new Date()
    })
  }
}