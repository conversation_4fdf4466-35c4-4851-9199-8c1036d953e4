import aws from 'aws-sdk'
import { injectable } from 'helocore'
import config from '../config'
import LoggerService from './LoggerService'

process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = '1'

@injectable()
export default class AWSS3Service {
  s3: aws.S3 = new aws.S3({
    region: config.AWS_S3_REGION,
    accessKeyId: config.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: config.AWS_S3_SECRET_ACCESS_KEY,
    signatureVersion: 'v4'
  })

  constructor(
    private readonly loggerService: LoggerService
  ) { }

  async getPresignedUrl(mediaName: string, traceId: string, bucketName = 'helorobo') {
    const url = await this.s3.getSignedUrlPromise('putObject', {
      Bucket: bucketName,
      Key: mediaName,
      StorageClass: 'ONEZONE_IA'
    }) // string olarak tek kullanımlık url döner

    this.loggerService.info({
      trace_id: traceId,
      message: 'url oluşturuldu',
      data: JSON.stringify({
        media_name: mediaName,
        bucket_name: bucketName,
        url: url
      })
    })
    return url
  }

}
