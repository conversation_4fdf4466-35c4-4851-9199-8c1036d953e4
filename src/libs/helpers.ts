import { nanoid } from 'nanoid'

export default {
  generateHash(length = 24) {
    return nanoid(length).toLowerCase()
  },

  tokenExtractor(authorizationHeader: string): string {
    if (!authorizationHeader) {
      throw new Error('Authorization Header is not Found.')
    }

    const token = authorizationHeader.split('Bearer ')[1];
    if (!token) {
      throw new Error('Token is not Found.')
    }

    return token
  }
}
